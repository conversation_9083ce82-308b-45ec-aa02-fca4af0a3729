import { actions, storage, store } from '@/store/index'
import { tryPromise } from '@/utils/tools/common/index'
import { isIos } from '@/utils/tools/validator/index'
import { getPageCode } from '@/utils/helper/resourceBit/index'
import { dealDialogRepByApi, getRdTodayPopkey, setRdTodayPopkey, uploadDialog } from '@/utils/helper/dialog/index'
import { midPhoneTimeMsg } from './index'

export * as contactCard from './contactCard'

type IResumeTelChatData = {
  /** 子名片ID */
  uuid: string,
  /** 场景值 1-简历详情；2-联系记录(我联系的人)；3-消费记录；4-关注工友详情；5-评价邀约；6-简历列表 */
  scene?: number,
  // 复制真实号码 1-是；0-否
  copyRealTel?: number,
  /** 是否返回中间号；0-真实号码；1-中间号  默认 1 */
  isPrivacy?: number,
  /** 是否获取拨打弹窗；0-不获取弹窗；1-获取弹窗   默认 1 */
  isPopup?: number,
  /** 弹窗无需弹出列表 */
  popupList?: Array<string>
  /** 电话聊天前置弹窗 /resume/v1/resumeTelChat/getTelChatPrePopup请求的额外参数-5.0.0版本差异化定价新增 */
  popParams?: {
    /** 工种列表, 透传前端检索工种工种 */
    occupations?: number[],
    /** 城市列表, 透传前端城市列表 */
    cities?: number[],
  }
  /** 算法id */
  algorithmId?: string
  /** 职位id */
  jobId?: string
  /** 职位草稿id（待发布） */
  jobDraftId?: string
  /** 场景值 */
  sceneV2?: string
  // 收藏信息来源 0-无来源 1-搜索
  collectInfoSource?: number
}

type IHandleErrorData = {
  success?: Function,
  fail?: Function
  // 重新请求
  requestAgain?: Function,
  failReport?: Function
  successReport?: Function
}

type IOperExtParamsData = {
  query?: {
    /** 来源(从哪个页面进入拨打电话) */
    type?: string
  }
  /** 跳转到发布招工的路由参数 */
  fastQuery?: {
    /** 工种id: '301,302' */
    occV2: string
  } // 发布招工页面的路由参数
  /** 是否已拨打电话,用于判断是否需要请求拨打电话前置弹框接口 */
  hasShowPhone?: boolean
  /** 当前页面名称 */
  pageName?: string

  /** 判断拨打的是真实号码还是中间号 0-真实号码；1-中间号 */
  isPrivacy?: number
}

/** 安全号-v1.4.3-弹框标识（老板端） */
export const newResumeMidPops = () => {
  return ['call_erxuanyi_B', 'call_anquanhao_B', 'call_zhenshihao_B']
}

/**
 * 拨打手机号
 * @param param 子名片ID
 * @param params[popParams] 电话聊天前置弹窗请求的额外参数[可选] 5.0.0版本差异化定价新增
 * @param isPrivacy 是否返回中间号；0-真实号码；1-中间号 默认 1
 * @param isPopup 是否获取拨打弹窗；0-不获取弹窗；1-获取弹窗 默认 1
*/
export const resumeTelV3 = (params: IResumeTelChatData, extparams: IOperExtParamsData = {}, buriedPointData: any = {}): any => {
  const { uuid, scene: nScene, isPrivacy = 1, isPopup = 1, popupList, copyRealTel = 0, jobId, jobDraftId, sceneV2, collectInfoSource } = params
  const { popParams: _popParams = {} } = params
  const scene = nScene || getScene()
  const nParams: IResumeTelChatData = { uuid: `${uuid}`, scene, isPrivacy, isPopup, copyRealTel, jobId, jobDraftId, sceneV2 }
  if (collectInfoSource) {
    nParams.collectInfoSource = collectInfoSource
  }
  return new Promise(async (resolve, reject) => {
    let res: any = {}
    if ((!extparams.hasShowPhone && isPopup == 1)) {
      const telPrePopupList = getRdTodayPopkey('telPrePopup')
      const popParams: any = { uuid: `${uuid}`, scene, jobId, jobDraftId, sceneV2, copyRealTel, ...(_popParams || {}), popupList: [...(popupList || []), ...(telPrePopupList || [])] }
      if (collectInfoSource) {
        popParams.collectInfoSource = collectInfoSource
      }
      res = await tryPromise(wx.$.javafetch['POST/resume/v3/resumeTelChat/getTelChatPrePopup'](popParams, { isNoToken: true, hideMsg: true }), {})
    }
    const { dialogData } = res.data || {}
    let { dialogIdentify = '' } = dialogData || {}
    const code = ['0', '200', '10000', '10001']
    const { backend_id } = buriedPointData || {}
    if (backend_id) {
      nParams.algorithmId = backend_id
    }
    if (!(code.includes(`${res.code}`) && dialogIdentify)) {
      res = await wx.$.javafetch['POST/resume/v3/resumeTelChat/getTelChat']({ ...nParams, ..._popParams })
      const { dialogData: telDialogData } = res.data || {}
      const { dialogIdentify: telDialogIdentify } = telDialogData || {}
      dialogIdentify = telDialogIdentify
    }

    if (dialogIdentify && !newResumeMidPops().includes(dialogIdentify)) {
      wx.hideLoading()
      handleErrorBeforeCallV3(
        res,
        { ...extparams },
        {
          fail: () => {
            reject('')
          },
          requestAgain: (res) => {
            const { hasShowPhone = true, popupList: nPopupList } = res || {}
            const nExtparams = { ...extparams }
            resumeTelV3({ ...params, ...res.param, popupList: [...(popupList || []), ...(nPopupList || [])] }, { ...nExtparams, hasShowPhone }, buriedPointData).then((cRes) => {
              resolve(cRes)
            }).catch(() => {
              resolve({})
            })
          },
        },
        params,
      )
      return
    }
    resolve(res)
  })
}

const getScene = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  switch (currentPage.route) {
    case 'subpackage/resume/detail/index':
      return 1
    case 'subpackage/member/myContactHistory/index':
      return 2
    case 'subpackage/member/integral/list/index':
      return 3
    case 'subpackage/member/evaluation/index':
    case 'subpackage/member/invite-evalute/index':
      return 5
    case 'pages/resume/index':
      return 6
    default:
      return 0
  }
}

/** 用于处理获取完整手机号时的所有异常情况
 * @extparams.fastQuery 跳转到发布招工页面的路由参数
 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export async function handleErrorBeforeCallV3(res, extparams: IOperExtParamsData = {}, fn: IHandleErrorData = {}, params:any = {}) {
  const { failReport } = fn || {}
  const { jobId, jobDraftId } = params || {}
  if (!res) {
    failReport && failReport()
    wx.$.msg('拨打失败，请稍后重试')
    return
  }

  const { dialogData } = res.data || {}
  let { dialogIdentify } = dialogData || {}
  let contentRep = dialogData && dialogData.template
  if (!dialogIdentify && res.data) {
    dialogIdentify = res.data.dialogIdentify
    contentRep = res.data.template
  }
  let popup
  if (dialogIdentify) {
    popup = await dealDialogRepByApi(dialogIdentify, contentRep, '', {}, { isNoLimit: true })
  }
  // 我已知晓，今日不再提醒 特殊弹框处理
  const checkPops = ['ckjlyckjlqy1', 'phone_out1', 'goutongfufeitishi3', 'qzqykc']
  if (checkPops.includes(dialogIdentify)) {
    wx.hideLoading()
    const confirmPopParam: any = {}
    const { currDialogConfig } = popup || {}
    const { businessDetail } = currDialogConfig || {}
    const { paramContent } = businessDetail || {}
    if (dialogIdentify == 'phone_out1' || dialogIdentify == 'ckjlyckjlqy1' || dialogIdentify == 'qzqykc') {
      const { surplusTimes } = contentRep || {}
      confirmPopParam.content = paramContent || `您当前剩余${surplusTimes}次电话直拨权益，立即联系将扣除1次`
      confirmPopParam.confirmText = '立即联系'
      if (dialogIdentify == 'ckjlyckjlqy1') {
        confirmPopParam.confirmText = '联系对方'
      }
    } else if (dialogIdentify == 'goutongfufeitishi3' || dialogIdentify == 'jobPricingFeesPopV3') {
      const { integral } = contentRep || {}
      confirmPopParam.content = paramContent || `本次联系需消耗${integral}积分`
      confirmPopParam.confirmText = '联系对方'
    }
    const popCode = dialogIdentify
    const pageCode = getPageCode()

    uploadDialog({ action: 1, popCode })
    wx.$.confirm({
      ...confirmPopParam,
      cancelText: '取消',
      checkText: '我已知晓，今日不再提醒！',
      cancelIcon: true,
    }).then(({ isCheck }) => {
      uploadDialog({ action: 3, popCode, text: confirmPopParam.confirmText })
      const re: any = {
      }
      if (isCheck) {
        setRdTodayPopkey(pageCode == 'im_conversation' ? 'expeditedDial' : 'telPrePopup', dialogIdentify)
      } else {
        re.popupList = [dialogIdentify]
      }
      fn.requestAgain && fn.requestAgain(re)
    }).catch(({ isCheck }) => {
      failReport && failReport()
      isCheck && setRdTodayPopkey(pageCode == 'im_conversation' ? 'expeditedDial' : 'telPrePopup', dialogIdentify)
      uploadDialog({ action: 2, popCode, text: '取消' })
    })
    return
  }

  switch (String(dialogIdentify)) {
    case 'jifenbuzutishi':
      failReport && failReport()
      fn.fail && fn.fail()
      if (popup) {
        wx.$.showModal({
          ...popup,
          success: (suc) => {
            if (suc.jumpEventType != 3) {
              // eslint-disable-next-line no-lonely-if
              if (isIos()) {
                wx.$.r.push({ path: '/subpackage/member/getintegral/index' })
              } else {
                // 积分充值页
                wx.$.r.push({
                  path: '/subpackage/recharge/recharge/index',
                  query: {
                    isFromPage: 'CLResumePhone',
                  },
                })
              }
            }
          },
        })
      }
      break
    case 'ZZ-TSYLX':
      if (popup) {
        wx.hideLoading()
        wx.$.showModal({
          ...popup,
          success: ({ routePath }) => {
            if (routePath == 're-contactOtherSide') {
              const re = {
                hasShowPhone: false,
                popupList: ['ZZ-TSYLX'],
                // param: {
                //   isPopup: 1,
                // },
              }
              fn.requestAgain && fn.requestAgain(re)
            } else {
              failReport && failReport()
              fn.fail()
            }
          },
        })
      }
      break
    case 'goutongmianfeitishi':
    case 'goutongfufeitishi':
    case 'goutongfufeitishi2': // 2023-05-15 新增
    case 'bodatixing':
    case 'fuzhihaomaPAY':
    case 'mpgoutongfukoudingjia': // 因您上次联系已退费, 主要用在拨打记录也的我联系的人
    case 'resumeReconfirmPopV1': // 因您上次联系已退费,重新查看扣费弹窗
    case 'resumeReconfirmPopV2': // 因您上次联系已退费,复制号码重新扣费弹窗
    case 'mpgoutongfufeitishidingjia': // 差异化定价弹框-联系对方需消耗{integral}积分
    case 'chakanyouhuibiaoshi':
    case 'ckjlyckjlqy':
    case 'ckjlytfyckjlqy': // 有电话直拨权益，上次已退费
    case 'ckjljfczydgmbdhy': // 无会员、无电话直拨权益，未退费，积分充足，引导开通会员
    case 'ckjljfczydgmck': // 有会员，无电话直拨权益，未退费，积分充足，引导开通次卡
    case 'ckjlytfjfczydgmck': // 有会员，无电话直拨权益，上次已退费，积分充足，引导开通次卡
    case 'ckjlytjfczydgmbdhy':// 无会员，无电话直拨权益，上次已退费，积分充足，引导开通会员
      if (popup) {
        wx.hideLoading()
        wx.$.showModal({
          ...popup,
          success: (resSm) => {
            if (resSm.jumpEventType == 4) {
              const re: any = {
                popupList: [dialogIdentify],
              }
              if (dialogIdentify == 'fuzhihaomaPAY') {
                re.param = { isPopup: 0 }
              }
              fn.requestAgain && fn.requestAgain(re)
            }
          },
        })
      } else {
        failReport && failReport()
        fn.fail && fn.fail()
      }
      break
    default:
      if (popup) {
        wx.hideLoading()
        // zhanghaoyichang:屏蔽   lainxigongrenshimingtishi:企业认证
        wx.$.showModal({
          ...popup,
          success: async (result) => {
            const routePath = result.routePath || ''
            // eslint-disable-next-line sonarjs/no-nested-switch
            switch (routePath) {
              case 'contactOtherSide': {
                const re: any = {
                  popupList: [dialogIdentify],
                }
                fn.requestAgain && fn.requestAgain(re)
                break
              }
              case 'confirm': {
                failReport && failReport()
                fn.fail && fn.fail()
                wx.$.r.push({
                  // eslint-disable-next-line sonarjs/no-duplicate-string
                  path: '/subpackage/recruit/fast_issue/index/index',
                  query: extparams.fastQuery || {},
                })
                break
              }
              case 'toEarnPoints': { // publishIntegralLack 积分不足弹窗 获取积分按钮
                failReport && failReport()
                fn.fail && fn.fail()
                wx.$.toGetIntegral({
                  isFromPage: 'CLResumePhone',
                })
                break
              }
              case 'publishJob': { // forcePublishJob: 弹框标识
                failReport && failReport()
                // 23245113【新增】发布招工-V1.9.0-B角色按工种强制发布
                await store.dispatch(actions.otherActions.setState({
                  recruitBackSource: {
                    path: `/${wx.$.r.getCurrentPage().route}`,
                    query: wx.$.r.getQuery(),
                    params: wx.$.r.getParams(),
                  },
                }))
                // 存store 跳转发布页面
                wx.$.r.push({
                  path: '/subpackage/recruit/fast_issue/index/index',
                  query: extparams.fastQuery || {},
                })
                break
              }
              case 'publish': {
                failReport && failReport()
                wx.$.r.push({
                  path: '/subpackage/recruit/fast_issue/index/index',
                  query: { occV2: contentRep.occIds },
                })
                break
              }
              case 'editRecruit': {
                failReport && failReport()
                wx.$.r.push({
                  path: '/subpackage/recruit/jisu_issue/index',
                  query: { id: jobId },
                })

                break
              }
              case 'editDraftRecruit': {
                failReport && failReport()
                wx.$.nav.push('/subpackage/recruit/edit-draft/index', { draftId: jobDraftId })
                break
              }
              default: {
                failReport && failReport()
                fn.fail && fn.fail()
              }
            }
          },
        })
        break
      }
      if (fn && fn.success) {
        fn.success()
      } else {
        failReport && failReport()
        wx.hideLoading()
        wx.$.msg(res.message)
      }
  }
}

type IMidCallData = {
  /** 回调中间号弹框信息 */
  callPopBack?: Function
  /** 拨打中间号 */
  callMidPhone?: Function
  /** 拨打真实或中间号码 */
  callRealPhone?: Function
  /** 点击拨打电话 */
  callPhone?: Function
  /** 发起电话成功 */
  callPhoneOk?: Function
  /** 拨打失败埋点 */
  failReport?: Function
  /** 拨打成功埋点 */
  successReport?: Function
}
/** 中间号相关弹窗--逻辑 */
export const operationMidCallV3 = async (data, extParams: IOperExtParamsData = {}, fn: IMidCallData = {}) => {
  if (!data) {
    wx.$.msg('拨打失败，请稍后重试')
    return
  }
  const { callPopBack } = fn || {}
  const { dialogIdentifyDefault: dialogKy, tel, dialogData, showRealTel } = data
  const { dialogIdentify = '', template } = dialogData || {}
  // let dialogIdentify = 'call_erxuanyi_B'
  const { query, hasShowPhone } = extParams
  if (dialogKy || dialogIdentify) {
    let popContent = data.popContent?.popupOne || {}
    let newContent = {}
    let { popType } = data

    if (newResumeMidPops().includes(dialogIdentify || dialogKy)) {
      const popup = await dealDialogRepByApi(dialogIdentify || dialogKy, template)
      if (popup && callPopBack) {
        storage.setItemSync('showRealTelState', showRealTel)
        callPopBack(popup)
        return
      }
    }
    if (query && query.type === 'whoContacted') { // 从谁联系过我的列表进入
      popContent = [
        {
          color: '#000000',
          content: '为保证您的权益，请使用鱼泡安全号联系！',
        },
      ]
      popType = 4
    } else if (data.popContent && (dialogKy == 'tuifeierxuanyi' || dialogKy == 'fufeierxuanyi')) {
      newContent = {
        call_privacy_tel: data.popContent.callPrivacyTel,
        call_real_tel: data.popContent.callRealTel,
      }
      popType = 1
    } else if (data.popContent && (dialogKy == 'anquanhao' || dialogKy == 'tuifeianquanhao')) {
      newContent = {
        call_privacy_tel: data.popContent.callPrivacyTel,
      }
      popType = 2
    } else if (dialogKy == 'zhenshihaoma') {
      // 如果安全号配置打开，就走中间号逻辑
      if (hasShowPhone) {
        fn.callRealPhone && fn.callRealPhone()
        return
      }
      let ext = {}

      if (data.popContent && data.popContent.onlyRealTel) {
        ext = data.popContent.onlyRealTel
      }
      uploadDialog({ popCode: dialogKy, action: 1, text: '打开' })
      // 需要展示拨打前确认身份弹窗(鱼泡提醒弹框)
      wx.$.confirm({
        type: 'resumeIdentity',
        cancelText: '暂不提示',
        confirmText: '拨打电话',
        maskClose: true,
        ext,
      }).then(() => {
        uploadDialog({ popCode: dialogKy, action: 3, text: '拨打电话' })
      }).catch(() => {
        uploadDialog({ popCode: dialogKy, action: 3, text: '暂不提示' })
      }).finally(() => {
        fn.callRealPhone && fn.callRealPhone()
      })
      return
    } else {
      fn.callRealPhone && fn.callRealPhone(1)
      return
    }
    storage.setItemSync('showRealTelState', showRealTel)
    callPopBack && callPopBack({
      type: popType,
      content: popContent,
      newContent,
      contentData: {
        has_expense_integral: data.has_expense_integral || (data.popContent && data.popContent.hasExpenseIntegral) || 0,
        is_expense_integral: data.is_expense_integral || (data.popContent && data.popContent.isExpenseIntegral) || 0,
      },
    })
  } else if (tel) {
    fn.callPhoneOk && fn.callPhoneOk()
    const { timeRemaining } = data || {}
    if (extParams && extParams.isPrivacy == 1 && timeRemaining) {
      await midPhoneTimeMsg(timeRemaining)
    }
    setTimeout(() => {
      wx.$.u.callPhone(tel)
    }, 200)
  } else {
    fn.callRealPhone && fn.callRealPhone(1)
  }
}

// 默认为已拨打,拨打电话处理
export function resumeMidTelV3(params, ext: any = {}, fn: IMidCallData = {}, buriedPointData: any = {}) {
  const { uuid, isPrivacy = 1, isPopup = 1, jobId, jobDraftId, sceneV2 } = params
  const { failReport, successReport } = fn || {}
  ext && ext.loading && wx.showLoading({ title: '联系中...' })
  resumeTelV3(
    { ...params, isPrivacy, isPopup, jobId, jobDraftId, sceneV2 },
    { ...ext },
    buriedPointData,
  ).then((res) => {
    wx.hideLoading()
    const { data } = res || {}
    if (!data) {
      failReport && failReport()
      return
    }
    const { tel, dialogIdentifyDefault, dialogData } = data || {}
    const { dialogIdentify } = dialogData || {}
    successReport && successReport(res)
    if (tel || dialogIdentifyDefault || newResumeMidPops().includes(dialogIdentify)) {
      operationMidCallV3(
        { ...data },
        { isPrivacy },
        {
          callPopBack: (pop) => {
            fn.callPopBack && fn.callPopBack(pop)
          },
          callRealPhone: (isPrivacy = 0) => {
            const next = { ...ext }
            resumeMidTelV3.call(this, { uuid, isPrivacy, isPopup: 0, jobId, jobDraftId, sceneV2 }, next, {}, buriedPointData)
          },
          callPhone: () => {
            const next = { ...ext }
            resumeMidTelV3.call(this, { uuid, jobId, jobDraftId, sceneV2 }, next, {}, buriedPointData)
          },
          callPhoneOk: () => {
            this.setData({
              showMiddleVisible: '',
              phoneCallTitle: '',
            })
          },
        },
      )
    } else if (!tel && !dialogIdentify && !dialogIdentifyDefault) {
      resumeMidTelV3.call(this, { uuid, isPrivacy: 0, isPopup: 0, jobId, jobDraftId, sceneV2 }, ext, {}, buriedPointData)
    }
  }).catch(() => {
    failReport && failReport()
  })
}

/**
 * 中间号拨号有效时长提醒 (此方法已迁移到D:\YpProject\yp-mini\src\lazy\common\midphone\index.ts )
 */
// export function midPhoneTimeMsg(timeRemaining) {
//   return new Promise((resolve) => {
//     if (timeRemaining) {
//       wx.$.msg(`此号码为鱼泡安全号，${timeRemaining}分钟有效，请尽快联系对方`)
//       // 这里延迟1.2秒返回
//       setTimeout(() => {
//         resolve({ status: true })
//       }, 1000)
//     } else {
//       resolve({ status: false })
//     }
//   })
// }

type IResumeImChatData = {
  // 子名片uuid
  uuid: string
  // 场景值 0-未知；1-简历详情；2-简历列表
  scene?: number
  // 职位id
  jobId?: string
  // 职位草稿id（待发布）
  jobDraftId?: string
  // 场景值
  sceneV2?: string
  // 弹出列表
  popupList?: Array<string>
  // 收藏信息来源 0-无来源 1-搜索
  collectInfoSource?: number
}

type IOperImExtParamsData = {
  cpShow?: boolean
  /** 跳转到发布招工的路由参数 */
  fastQuery?: {
    /** 工种id: '301,302' */
    occV2: string
  }
  /** 简历列表选中的tab对应的职位ID */
  selectPositionTabId?: string
  relatedInfoId?: string
  /** 1-谁看过我进的简历详情,2在招职位Tab,3在招聘职位进的简历详情 */
  bossPickJobScene?: number
  // 聊一聊权益
  hasImChatRight?:boolean
}

type IMchatData = {
  /** 失败 */
  fail?: Function
  /** 成功 */
  success?: Function
  /** 选择职位弹框回调 */
  selectpostionback?: Function
  /** 请求扣费接口 */
  reGetImChat?: Function
}

/**
 * 找活详情或列表 点击聊一聊
 * */
export async function getImChatPre(params: IResumeImChatData, extparams: IOperImExtParamsData = {}, fn: IMchatData = {}) {
  const { uuid, scene, jobId, jobDraftId, sceneV2 = 22, popupList, collectInfoSource } = params || {}
  const { selectpostionback, reGetImChat } = fn || {}
  const oplist = getRdTodayPopkey('ImChatPrePopup')
  const nParams: any = { uuid, scene, jobId, jobDraftId, sceneV2, popupList: [...(oplist || []), ...(popupList || [])] }
  if (collectInfoSource) {
    nParams.collectInfoSource = collectInfoSource
  }
  return new Promise(async (resolve, reject) => {
    const res = await tryPromise(
      wx.$.javafetch['POST/resume/v3/resumeImChat/getImChatPrePopup'](
        nParams,
        { isNoToken: true, hideMsg: true },
      ),
      {},
    )
    const { code, data, message, error } = res || {}
    const { dialogData } = data || {}
    if (code != 0) {
      const { dialogIdentify = '', template } = dialogData || {}
      const { contentRep } = template || {}
      if (dialogIdentify) {
        const popup = await dealDialogRepByApi(dialogIdentify, contentRep || template, '', {}, { isNoLimit: true })
        const checkPops = ['im_qysytx1', 'chat_out1']
        if (checkPops.includes(dialogIdentify)) {
          const confirmPopParam: any = {}
          const { currDialogConfig } = popup || {}
          const { businessDetail } = currDialogConfig || {}
          const { paramContent } = businessDetail || {}
          if (dialogIdentify == 'chat_out1' || dialogIdentify == 'im_qysytx1') {
            const { surplusTimes } = contentRep || {}
            confirmPopParam.content = paramContent || `您今日剩余${surplusTimes}次畅聊卡次数，立即沟通将扣除1次`
            confirmPopParam.confirmText = '立即沟通'
          }
          const popCode = dialogIdentify
          wx.hideLoading()
          uploadDialog({ action: 1, popCode })
          wx.$.confirm({
            ...confirmPopParam,
            cancelText: '取消',
            checkText: '我已知晓，今日不再提醒！',
            cancelIcon: true,
          }).then(({ isCheck }) => {
            uploadDialog({ action: 3, popCode, text: confirmPopParam.confirmText })
            isCheck && setRdTodayPopkey('ImChatPrePopup', dialogIdentify)
            getImChat(params, extparams, {
              fail: () => {
                reject()
              },
              success: (sucRes) => {
                resolve(sucRes)
              },
              selectpostionback,
              reGetImChat,
            })
          }).catch(({ isCheck }) => {
            isCheck && setRdTodayPopkey('ImChatPrePopup', dialogIdentify)
            uploadDialog({ action: 2, popCode, text: '取消' })
          })
          return
        }
        if (popup) {
          wx.hideLoading()
          wx.$.showModal({
            ...popup,
            success: async (resSm) => {
              const routePath = resSm.routePath || ''
              switch (routePath) {
                case 're-contactOtherSide': {
                  getImChatPre({ ...params, popupList: [...(popupList || []), dialogIdentify] }, extparams).then((sucRes) => {
                    resolve(sucRes)
                  }).catch(() => {
                    reject()
                  })
                  break
                }
                case 'imChat': {
                  getImChat(params, extparams, {
                    fail: () => {
                      reject()
                    },
                    success: (sucRes) => {
                      resolve(sucRes)
                    },
                    selectpostionback,
                    reGetImChat,
                  })
                  break
                }
                case 'toEarnPoints': { // publishIntegralLack 积分不足弹窗 获取积分按钮
                  reject()
                  wx.$.toGetIntegral({
                    isFromPage: 'CLResumeChat',
                  })
                  break
                }
                case 'publishJob': {
                  reject()
                  // 23245113【新增】发布招工-V1.9.0-B角色按工种强制发布
                  await store.dispatch(actions.otherActions.setState({
                    recruitBackSource: {
                      path: `/${wx.$.r.getCurrentPage().route}`,
                      query: wx.$.r.getQuery(),
                      params: wx.$.r.getParams(),
                    },
                  }))
                  // 存store 跳转发布页面
                  wx.$.r.push({
                    path: '/subpackage/recruit/fast_issue/index/index',
                    query: extparams.fastQuery || {},
                  })
                  break
                }
                case 'publish': {
                  const { occIds } = template || {}
                  wx.$.r.push({
                    path: '/subpackage/recruit/fast_issue/index/index',
                    query: { occV2: occIds },
                  })
                  break
                }
                case 'editRecruit': {
                  reject()
                  wx.$.r.push({
                    path: '/subpackage/recruit/jisu_issue/index',
                    query: { id: jobId },
                  })

                  break
                }
                case 'editDraftRecruit': {
                  reject()
                  wx.$.nav.push('/subpackage/recruit/edit-draft/index', { draftId: jobDraftId })
                  break
                }
                default:
                  reject()
              }
            },
          })
          return
        }
      }
      if (error) {
        reject()
        wx.$.msg(message)
        return
      }
    }

    getImChat(params, extparams, {
      fail: () => {
        reject()
      },
      success: (sucRes:any = {}) => {
        resolve(sucRes)
      },
      selectpostionback,
      reGetImChat,
    })
  })
}

export async function getImChat(params: IResumeImChatData, extparams: IOperImExtParamsData = {}, fn: IMchatData = {}) {
  const { uuid, scene, jobId, jobDraftId, sceneV2, collectInfoSource } = params || {}
  const { fail, success, selectpostionback, reGetImChat } = fn || {}
  let relatedJobId = ''
  const { hasImChatRight } = extparams || {}
  // const { cpShow, bossPickJobScene, selectPositionTabId, relatedInfoId } = extparams || {}
  // if ((cpShow && selectpostionback) || bossPickJobScene) {
  if ((!jobId || jobId == '0') && (!jobDraftId || jobDraftId == '0') && sceneV2 && !hasImChatRight) {
    // const nParams: any = { uuid }
    // if (bossPickJobScene) {
    //   nParams.bossPickJobScene = bossPickJobScene
    // }
    // if (bossPickJobScene == 1) {
    //   nParams.jobId = relatedInfoId
    // } else if (bossPickJobScene == 2 || bossPickJobScene == 3) {
    //   nParams.jobId = selectPositionTabId
    // }
    const { isOk, jobId: nRelatedJobId } = await getBossPickJob(
      {
        selectpostionback,
        reGetImChat,
      },
    )
    if (isOk) {
      return
    }
    relatedJobId = nRelatedJobId || ''
  }

  const rqParams:any = { uuid, scene, jobId, jobDraftId, sceneV2 }
  if (collectInfoSource) {
    rqParams.collectInfoSource = collectInfoSource
  }
  wx.$.javafetch['POST/resume/v3/resumeImChat/getImChat'](rqParams, { isNoToken: true, hideMsg: true }).then(async (res) => {
    const { data, code, message, error } = res
    const { dialogData, isChatSearchPurchase } = data || {}
    if (code != 0) {
      const { dialogIdentify = '', template } = dialogData || {}
      const { contentRep } = template || {}
      if (dialogIdentify) {
        const popup = await dealDialogRepByApi(dialogIdentify, contentRep || template, '', {}, { isNoLimit: true })
        if (popup) {
          fail && fail()
          wx.$.showModal({
            ...popup,
          })
          return
        }
      }
      if (error) {
        fail && fail()
        wx.$.msg(message)
        return
      }
    }
    success && success({ relatedJobId: relatedJobId && relatedJobId != '0' ? relatedJobId : '', isChatSearchPurchase })
  }).catch((err) => {
    const { message } = err || {}
    if (message) {
      wx.$.msg(message)
    }
    fail && fail()
  })
}

/** 判断是否需要弹出选择职位弹框 */
export async function getBossPickJob(fn: IMchatData = {}) {
  const { selectpostionback, reGetImChat } = fn || {}
  const res = await tryPromise(wx.$.javafetch['POST/reach/v2/im/chat/bossSelectJobDialog']({}, { isNoToken: true, hideMsg: true }), {})
  const { data } = res
  const { dialogData, jobId } = data || {}
  const { dialogIdentify = '' } = dialogData || {}
  if (['zwxz-zbfs', 'zwxz-bxzw'].includes(dialogIdentify) && selectpostionback) {
    selectpostionback({ dialogIdentify, reGetImChat })
    return { isOk: true }
  }
  return { isOk: false, jobId }
}
