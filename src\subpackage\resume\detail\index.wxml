<skeleton wx:if="{{!hideSkeleton}}" /> 
<view class="container">
  <view class="slide-tip" wx:if="{{showSlideTip}}" bind:tap="onSlideTip">
    <image class="slide-gesture" src="https://cdn.yupaowang.com/yupao_mini/yp_mini_slide-gesture.png" bind:load="onSlideTip" />
    <view class="tip-text">左右滑动，可切换简历</view>
  </view>
  <view>
    <view class="custom-header" id="custom-header">
      <custom-header fixed="{{false}}" header-out="header-out" home-class="home-class" iscJn>
        <view class="header-title" slot="before">{{ ''}}</view>
        <view wx:if="{{hideSkeleton && !isEmpty}}" class="header-icon" slot="after">
          <icon-font wx:if="{{detail.collected}}" bind:tap="onAttention" type="yp-icon_collect_hig" size="48rpx" color="#FFB700" />
          <icon-font wx:else bind:tap="onAttention" type="yp-icon_collect_nor" size="48rpx" color="rgba(0, 0, 0, 0.85)" />
        </view>
      </custom-header>
      <change-related-job 
        show="{{login && detail.resumeSubUuid && btnObj.chat.isShow && query.type != 'groupConversation' &&  query.type != 'whoContactedMeToResume' && !selectPositionTabId && !notContact}}"
        id="changeRelatedJob" 
        dialogIdentify="{{cpDialogIdentify}}" 
        detail="{{detail}}" 
        bind:nosend="onCpNoSend" 
        bind:confirm="onCpConfirm" 
        bind:selected="onCpSelected"
        bind:close="onCpClose"
      />
   </view>
   <related-job wx:if="{{hideSkeleton}}" query="{{query}}" bind:openDraft="onOpenDraft" bind:modifyJob="onModifyJob"></related-job  />
    <slide-wrap query="{{query}}" visible="{{!!slideAB}}">
      <view class="resume-content" wx:if="{{!isEmpty}}">
        <!-- <skeleton wx:if="{{!hideSkeleton}}" />  -->
        <block>
          <view class="section">
            <!-- 用户信息 -->
            <user-card-info hasRight="{{hasRight}}" query="{{query}}" maskAndVisible="{{maskAndVisible}}"  detail="{{detail}}" data-click_entry='2' bind:complain="onComplain"/>
            <!-- 附近适合您的工人 -->
            <recommend-workers wx:if="{{!notContact}}" show="{{showRecommendWorkers && !maskAndVisible.hidden.nearbyWorkers}}" query="{{query}}" resumeDetailNearbyListReqParams="{{resumeDetailNearbyListReqParams}}" bind:autoScrollToPageUISelector="autoScrollToPageUISelector" />
          </view>
          <!-- 底部悬浮按钮 -->
          <block wx:if="{{!notContact}}">
            <view wx:if="{{showBottom}}" class="footer">
              <!-- 未登录引导浮窗 -->
              <no-login-float-pop wx:if="{{!login && loginTextGroup.resume_detail}}"  custom-class="boxClass {{isAndroid ? 'lb' : ''}}" text="{{loginTextGroup.resume_detail}}" pointId="7"/>
              <discount-tips 
              bind:discountClick="discountClick"  
              wx:if="{{ detail.isSpecialTel && (!detail.browseResp.viewed) && (btnObj.phone && btnObj.phone.isShow) }}"
              />
              <!-- 鱼泡VIP显示（会员横幅聊一聊时不展示（目前招工聊一聊没有定价免费，所以聊一聊显示就不展示横幅））  联系老板 -->
              <rec-bvip wx:elif="{{query.type != 'whoContacted' && !maskAndVisible.hidden.vipBanner}}" pageSource="{{query.nearbyWorkerListApiSource}}"  btnObj="{{btnObj}}"/>
              <block>
              <!-- 非系统简历 -->
              <view class="footer-cont">
                <view class="footer-right">
                  <!-- 分享 -->
                  <button class="share-box" open-type="share" bind:tap="onPointer">
                    <icon-font custom-class="normalWeight" color='#595959' type="yp-yp_mini_share" size="48rpx"/>
                    <view class="txt">分享</view>
                  </button>
                  <block>
                    <view wx:if="{{btnObj.chat.isShow}}" class="{{btnObj.phone.isShow?'chat-btn':'chat-btn-item'}}" bind:tap="onGoToChat">
                      <icon-font custom-class="btn-icon" type="yp-chat" size="36rpx" color="rgba(255, 255, 255, 1)" />
                      {{btnObj.chat.btnText}}
                    </view>
                    <view wx:if="{{btnObj.phone.isShow}}" class="btn-item" bind:tap="clickBottomPhone" data-click_entry='1'>
                      <view class="btn-item-cont">
                        <icon-font custom-class="btn-icon" type="yp-re_phone" size="36rpx" color="rgba(255, 255, 255, 1)" />
                        <!-- <icon-font custom-class="phone-icon" type="yp-icon_call" size="48rpx" /> -->
                        <block>
                          <text class="btn-text">{{query.type == 'groupConversation' ? '拨打电话' : btnObj.phone.btnText}}</text>
                          <!-- 剩余免费联系次数(只限面向工程类老板免费次数) -->
                          <view class="btn-call-count" wx:if="{{detail.freeCount > 1}}">
                            <text class="btn-call-count-text">{{detail.freeCount}}次</text>
                          </view>
                        </block>
                      </view>
                    </view>
                  </block>
                </view>
              </view>
              </block>
              <!-- 底部小横条 -->
              <view class="stripes" ></view>
            </view>
          </block>
          <block wx:else>
            <view wx:if="{{showBottom}}" class="footerW">
              <view class="xz-banner" wx:if="{{query.selectedTab.jobId}}">职位信息审核失败，请修改后重新提交</view>
              <guidance-toast wx:if="{{query.selectedTab.jobDraftId}}"  />
              <view class="xz-btn-box" bind:tap="onGoToPublish" data-text="{{query.selectedTab.jobId ? '修改职位': query.selectedTab.jobDraftId ? '打开职位': '发布职位'}}">
                <view class="xz-btn">
                  <image class="xz-btn-icon" src="https://cdn.yupaowang.com/null16103f421fbcb05e.png" />
                  {{query.selectedTab.jobId ? '修改职位': query.selectedTab.jobDraftId ? '打开职位': '发布职位'}} 立即沟通牛人
                </view>
              </view>
            <view class="stripes"></view>
          </view>
        </block>
      </view>
      <view class="empty" wx:else>
        <empty-box
          height="auto"
          img-class="no-data-img"
          title="{{emptyTitle}}"
          imgType='msg'
        />
      </view>
    </slide-wrap>
  </view>
  <!-- 浮标 -->
  <buoy-of-page buoyTop="{{buoyTop}}" buoyUnder="{{buoyUnder}}" wx:if="{{hideSkeleton}}" />
  <!-- 评分窗口 -->
  <score-evaluation-nw wx:if="{{!detail.isSelf && detail.resumeSubUuid && evaluateContentControl.show }}" isMidGetTel="{{isMidGetTel}}" isCooperation="{{true}}" special_type="{{detail.specialArea}}" type="resume" targetId="{{detail.resumeSubUuid}}" detail="{{detail}}" action="{{isMidGetTel.action}}" expressionKey="{{evaluateContentControl.expression}}" bind:submitsuccess="onEvalSubmitSuccess" bind:hide="onEvalClose" />
  <!-- 绘制分享卡片 -->
  <!-- 这个是painter组件使用 -->
  <painter wx:if="{{detail.specialArea == 3}}" palette="{{canvasTemplate}}" style="position: absolute; top: -9999rpx;" bind:imgOK="onImgOK" />
</block>
