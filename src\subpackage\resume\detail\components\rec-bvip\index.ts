import { storage } from '@/store/index'

/*
 * @Date: 2022-04-13 15:56:56
 * @Description: 找活新手引导组件
 */
Component({
  properties: {
    btnObj: {
      type: Object,
      value: {},
    },
    pageSource: {
      type: String,
      value: '',
    },
  },
  data: {
    bannerContent: '',
    bannerType: '',
    showBanner: false,
    banneBtn: '',
    isHide: false,
  },
  btnObj: undefined,
  observers: {
    btnObj(btnObj) {
      if (btnObj && (!this.btnObj || JSON.stringify(btnObj) != JSON.stringify(this.btnObj))) {
        this.initData()
        this.btnObj = btnObj
      }
    },
  },
  pageLifetimes: {
    show() {
      const { isHide } = this.data
      if (isHide) {
        this.initData()
        this.setData({ isHide: false })
      }
    },
    hide() {
      this.setData({ isHide: true })
      this.btnObj = undefined
    },
  },
  methods: {
    async initData() {
      const { login } = storage.getItemSync('userState')
      if (!login) {
        return
      }

      const currentPage = wx.$.r.getCurrentPage()
      console.log('this.data.pageSource', this.data.btnObj)

      const searchChat = 'member/v1/vipBanner/searchResumeDetail'
      let vipBannerRq = 'member/v1/vipBanner/resumeDetail'

      if (['ResumeSearchList'].includes(currentPage.data.query.nearbyWorkerListApiSource)) {
        vipBannerRq = searchChat
      }
      const res = await wx.$.javafetch[`POST/${vipBannerRq}`]()
      const { data } = res || {}
      const { bannerContent, bannerType } = data || {}
      const { btnObj } = this.data || {}
      let { showBanner } = data || {}
      let banneBtn = ''
      switch (bannerType) {
        case 1:
        case 2:
          banneBtn = '立即开通'
          break
        case 3:
        case 6:
          showBanner = showBanner && btnObj?.chat?.isShow
          banneBtn = ''
          break
        case 4:
          showBanner = showBanner && btnObj?.chat?.isShow
          banneBtn = '立即购买'
          break
        default:
          banneBtn = '去查看'
          break
      }
      this.setData({
        bannerContent,
        bannerType,
        showBanner,
        banneBtn,
      })
    },
    /**
     * 会员横幅点击事件
     * bannerType:
     * 1 按钮文案-立即开通；跳转-B端会员购买页
     * 2 按钮文案-立即开通；跳转-B端会员购买页
     * 3 按钮文案-查看权益；跳转-B端会员详情页
     * 4 按钮文案-立即购买；跳转-在线畅聊次卡购买页
     * 6 按钮文案-查看权益；跳转-B端会员购买页
     * 7 无会员无次数有购买记录
     * b端会员购买：/b-member-vip/order；B端会员会员详情页：/b-member-vip/detail；次卡购买：/b-member-vip/secondary；次卡详情：/b-member-vip/secondary-detail；购买记录：/b-member-vip/record
     */
    opVip() {
      const { showBanner, bannerType } = this.data
      if (showBanner) {
        let url = ''
        if (bannerType == 3) {
          return
        }
        if (bannerType == 7) {
          url = '/b-member-vip/record'
        } else if (bannerType == 4) {
          url = '/b-member-vip/secondary-detail?typeCard=5'
        } else {
          url = '/b-member-vip/order'
        }
        if (url) {
          url = `${url}${url.indexOf('?') > -1 ? '&' : '?'}vipFromPageSource=YUPAO_B_CHAT_CARD`
          // wx.$.collectEvent.event('vipCardClick', { content_texts: bannerContent, button_name: banneBtn, page_name: '会员中心' })
          wx.$.r.push({ path: `/subpackage/web-view/index?isLogin=true&url=${encodeURIComponent(url)}` })
        }
      }
    },
  },
})
